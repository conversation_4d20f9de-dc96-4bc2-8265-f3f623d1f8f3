# Use Python 3.13.5 slim image for smaller size and better security
FROM python:3.13.5-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH=/app

# Set the working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    --no-install-recommends \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Create a non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install gunicorn for serving the application
RUN pip install --no-cache-dir gunicorn==21.2.0

# Copy application code
COPY . .

# Change ownership of the app directory to the non-root user
R<PERSON> chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose the port that the app runs on
EXPOSE 8080

# Health check (simple Python import check since this is a Cloud Function)
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python -c "import main; print('Health check passed')" || exit 1

# Command to run the application using functions-framework
# The functions-framework will handle the HTTP server and route requests to your function
CMD exec functions-framework --target=process_email_campaign --port=8080 --host=0.0.0.0
