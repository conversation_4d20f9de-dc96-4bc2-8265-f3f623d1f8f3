import logging
import requests
import json
import os
import time
import base64
from datetime import datetime, timezone
import pytz
import sendgrid
from io import BytesIO
from concurrent.futures import Thread<PERSON><PERSON>Executor, as_completed
from sendgrid.helpers.mail import Attachment as SendGridAttachment
from sendgrid.helpers.mail import (Bcc, Category, Cc, ContentId, Disposition,
                                   FileContent, FileName, FileType, Mail,
                                   Personalization, To)
from sqlalchemy import text

# Import timezone utilities
from utils import utc_now, ist_now, to_utc, to_ist

# SendGrid configuration
SENDGRID_API_KEY = os.environ.get('SENDGRID_API_KEY')
DB_NAME = os.environ.get('DB_NAME')
MAIL_ID = os.environ.get("MAIL_ID", "<EMAIL>")

# Rate limiting and retry configuration
MAX_RETRIES = int(os.environ.get("MAX_RETRIES", 3))
RETRY_DELAY = float(os.environ.get("RETRY_DELAY", 2.0))
MAX_CONCURRENT_SENDS = int(os.environ.get("MAX_CONCURRENT_SENDS", 5))

# Create a persistent SendGrid client
sendgrid_client = sendgrid.SendGridAPIClient(api_key=SENDGRID_API_KEY)

# Configure logging
logger = logging.getLogger(__name__)

# Template parameter mappings for different conciliation templates
TEMPLATE_PARAMETER_MAPPINGS = {
    'termination_notice': [
        'client_name', 'borrower_name', 'loan_number', 'notice_date', 'notice_amount', 'payment_link'
    ],
    'demand_notice': [
        'borrower_name', 'client_name', 'loan_number', 'dishonour_date', 'notice_date', 'notice_amount', 'payment_link'
    ],
    'payment_request_notice': [
        'borrower_name', 'client_name', 'loan_number', 'notice_date', 'notice_amount', 'payment_link'
    ],
    'payment_reminder': [
        'borrower_name', 'client_name', 'loan_number', 'notice_date', 'notice_amount', 'payment_link'
    ],
    'sarfaeri_act_notice': [
        'borrower_name', 'client_name', 'payment_link'
    ],
    'conciliation_notice_1': [
        'client_name', 'date', 'start_time', 'end_time', 'case_manager_name', 'case_manager_email', 'link'
    ],
    'conciliation_notice_4': [
        'date', 'link'
    ],
    'conciliation_email_reminder': [
        'date', 'client_name', 'date', 'start_time', 'end_time','link'
    ]
}

def get_subject_from_template(template_id):
    """Get email subject from SendGrid template."""
    if hasattr(template_id, 'template_id'):
        template_id = template_id.template_id

    try:
        response = sendgrid_client.client.templates._(template_id).get()
        decoded_body = response.body.decode('utf-8')
        template_info = json.loads(decoded_body)
        # Extract subject from the template_info JSON
        subject = template_info.get('versions', [{}])[0].get('subject', 'No Subject')
        return subject
    except Exception as e:
        return "No Subject"

def send_sendgrid_message_and_save_to_database(pool, message, to_email, root_template_id, retry_count=0):
    """Send message via SendGrid and save record to database with retry logic."""
    try:
        subject = None
        dispute_id = None
        is_co_borrower = False

        # Get subject from template
        if hasattr(message, 'template_id') and message.template_id:
            subject = get_subject_from_template(message.template_id)

        # Get dispute ID if available
        if hasattr(message, 'dispute_id'):
            dispute_id = message.dispute_id
            
        # Check if this is a co-borrower
        if hasattr(message, 'is_co_borrower'):
            is_co_borrower = message.is_co_borrower

        # Try to send email with retry logic
        try:
            response = sendgrid_client.send(message)
            print("******** Response status_code ********", response.status_code, "******* Response body *******", response.body, "******* Response Headers *******", {response.headers})

            # If rate limited (status 429), retry with backoff
            if response.status_code == 429 and retry_count < MAX_RETRIES:
                wait_time = RETRY_DELAY * (2 ** retry_count)
                time.sleep(wait_time)
                return send_sendgrid_message_and_save_to_database(pool, message, to_email, root_template_id, retry_count + 1)

            # Handle other server errors that might benefit from retrying
            elif response.status_code >= 500 and retry_count < MAX_RETRIES:
                wait_time = RETRY_DELAY * (2 ** retry_count)
                time.sleep(wait_time)
                return send_sendgrid_message_and_save_to_database(pool, message, to_email, root_template_id, retry_count + 1)

            # Handle successful response
            elif response.status_code == 202:  # SendGrid uses 202 for success
                # Retrieve the message_id from the response headers
                x_message_id = response.headers.get('X-Message-Id', 'unknown')
                timestamp = utc_now()  # Changed to UTC
                try:

                    # Save to database
                    with pool.connect() as connection:
                        insert_query = text("""
                            INSERT INTO sendgridlogs_sendgridmail (email, x_message_id, timestamp, dispute_id, subject, is_co_borrower, template_id)
                            VALUES (:email, :x_message_id, :timestamp, :dispute_id, :subject, :is_co_borrower, :template_id)
                            RETURNING id
                        """)
                        params = {
                            "email": to_email,
                            "x_message_id": x_message_id,
                            "timestamp": timestamp,
                            "dispute_id": dispute_id,
                            "subject": subject,
                            "is_co_borrower": is_co_borrower,
                            "template_id": root_template_id
                        }

                        result = connection.execute(insert_query, params)
                        inserted_id = result.fetchone()[0]  # Get the returned ID
                        connection.commit()
                except Exception as e:
                    logger.error(f"Error updating sendgridlogs_sendgridmail in database: {str(e)}")
                    return False

                return f'success\nstatus_code {str(response.status_code)}\nx_message_id {x_message_id}'

            # Handle permanent errors
            else:
                return f'error\nstatus_code {str(response.status_code)}\nbody {str(response.body)}'

        except requests.exceptions.RequestException as e:
            if retry_count < MAX_RETRIES:
                wait_time = RETRY_DELAY * (2 ** retry_count)
                time.sleep(wait_time)
                return send_sendgrid_message_and_save_to_database(pool, message, to_email, root_template_id, retry_count + 1)
            else:
                logger.error(f'error - Max retries reached: {str(e)}')
                return f'error\nMax retries reached: {str(e)}'

    except Exception as e:
        return f'error\nUnexpected error: {str(e)}'

def send_email_template(pool, to_email, template_id, dispute_id, root_template_id, substition_data={}, from_email=None, files=[], cced_emails=[], bcced_emails=[], custom_args={}, is_co_borrower=False, borrower_name=''):
    """Create and send an email using SendGrid templates with optional attachments."""
    # Set from_email based on IS_S21_NOTICE if not provided
    # from_email = from_email or MAIL_ID
            
    # Create the mail message with the determined from_email
    message = Mail(
        from_email=from_email
    )
    personalization = Personalization()
    personalization.add_to(To(to_email))
    for bcc_addr in bcced_emails:
        personalization.add_bcc(Bcc(bcc_addr))
    for ccE in cced_emails:
        personalization.add_cc(Cc(ccE))
    message.add_personalization(personalization)
    
    # Add dynamic template data
    dynamic_data = substition_data.copy()
    
    # Add borrower name to template data if provided
    if borrower_name:
        dynamic_data['borrower_name'] = borrower_name
    
    # Add co-borrower flag to template data
    dynamic_data['is_co_borrower'] = is_co_borrower
    
    message.dynamic_template_data = dynamic_data
    message.template_id = template_id
    
    # Add attachments if any
    attachments = []
    for file in files:
        if file and 'file' in file and file['file']:
            try:
                encoded = base64.b64encode(file['file']).decode()
                attachment = SendGridAttachment()
                attachment.file_content = FileContent(encoded)
                attachment.file_name = FileName(file['name'])
                attachment.disposition = Disposition('attachment')
                attachment.content_id = ContentId(''+file['name'])
                attachments.append(attachment)
            except Exception as e:
                logger.error(f"Error processing attachment {file.get('name', 'unknown')}: {str(e)}")
    if attachments:
        message.attachment = attachments

    # Add metadata to message object
    message.dispute_id = dispute_id
    message.is_co_borrower = is_co_borrower
    message.category = [Category(DB_NAME)]
    
    return send_sendgrid_message_and_save_to_database(pool, message, to_email, root_template_id)

def send_email(pool, email, template_id, dispute_id, root_template_id, is_s21_notice, client_name, files=[], is_co_borrower=False, borrower_name=''):
    """Simplified wrapper for send_email_template."""
    mail_id = None
    if is_s21_notice:
        mail_id = "<EMAIL>"
    else:
        mail_id = "<EMAIL>"

    substition_data = {'client_name': client_name}

    return send_email_template(
        pool=pool,
        to_email=email,
        template_id=template_id,
        dispute_id=dispute_id,
        root_template_id=root_template_id,
        from_email=mail_id,
        files=files,
        is_co_borrower=is_co_borrower,
        borrower_name=borrower_name,
        substition_data=substition_data
    )

def determine_template_type(is_termination_notice, is_demand_notice, is_payment_request_notice, sarfaeri_act_notice,conciliation_notice_1, 
                          conciliation_notice_4, conciliation_email_reminder):
    """
    Determine the template type based on the boolean flags.
    
    Returns:
        str: The template type identifier
    """
    if is_termination_notice:
        return 'termination_notice'
    elif is_demand_notice:
        return 'demand_notice'
    elif is_payment_request_notice:
        return 'payment_request_notice'
    elif sarfaeri_act_notice:
        return 'sarfaeri_act_notice'
    elif conciliation_email_reminder:
        return 'conciliation_email_reminder'
    elif conciliation_notice_1:
        return 'conciliation_notice_1'
    elif conciliation_notice_4:
        return 'conciliation_notice_4'
    else:
        return 'generic'

def create_substitution_data_from_parameters(email_parameters, template_type, custom_mapping=None):
    """
    Create substitution data dictionary from email parameters array.
    
    Args:
        email_parameters: List of parameter values
        template_type: Type of template (determines parameter mapping)
        custom_mapping: Optional custom parameter mapping list
        
    Returns:
        dict: Substitution data for SendGrid template
    """
    substitution_data = {}
    
    # Use custom mapping if provided, otherwise use predefined mapping
    if custom_mapping:
        param_keys = custom_mapping
    elif template_type in TEMPLATE_PARAMETER_MAPPINGS:
        param_keys = TEMPLATE_PARAMETER_MAPPINGS[template_type]
    else:
        # Fallback for unknown template types
        param_keys = [f'param_{i+1}' for i in range(len(email_parameters))]
    
    # Map parameters to keys
    for i, param in enumerate(email_parameters):
        if i < len(param_keys):
            substitution_data[param_keys[i]] = param
        else:
            # Handle extra parameters
            substitution_data[f'extra_param_{i+1}'] = param
    return substitution_data

def send_conciliation_email(pool, email, template_id, dispute_id, root_template_id, is_s21_notice=False, files=[], 
                          is_co_borrower=False, borrower_name='', email_parameters=None,
                          template_type=None, custom_parameter_mapping=None):
    """
    Send a conciliation email with flexible parameter mapping.
    
    Args:
        pool: Database connection pool
        email: Recipient's email address
        template_id: SendGrid template ID
        dispute_id: Dispute ID for tracking
        is_s21_notice: Boolean to determine config (default True for conciliation)
        files: Optional list of file attachments
        is_co_borrower: Boolean indicating if this is a co-borrower
        borrower_name: Name of the borrower
        email_parameters: List of parameters for the template
        template_type: Type of template (for automatic parameter mapping)
        custom_parameter_mapping: Custom list of parameter names for mapping
    
    Returns:
        Response from SendGrid API
    """
    mail_id = None
    if is_s21_notice:
        mail_id = "<EMAIL>"
    else:
        mail_id = "<EMAIL>"
    
    # Prepare substitution data
    substitution_data = {}
    
    if email_parameters:
        # Create substitution data from parameters using the specified mapping method
        substitution_data = create_substitution_data_from_parameters(
            email_parameters, 
            template_type or 'generic',
            custom_parameter_mapping
        )
    
    # Add borrower-specific data
    if borrower_name:
        substitution_data['borrower_name'] = borrower_name
    
    substitution_data['is_co_borrower'] = is_co_borrower
    
    return send_email_template(
        pool=pool,
        to_email=email,
        template_id=template_id,
        dispute_id=dispute_id,
        root_template_id=root_template_id,
        substition_data=substitution_data,
        from_email=mail_id,
        files=files,
        is_co_borrower=is_co_borrower,
        borrower_name=borrower_name
    )