# BFSI Cloud Function Email Notice Service

A Google Cloud Function for processing email campaigns with Python 3.13.5, designed for deployment on Cloud Run.

## 🚀 Features

- **Python 3.13.5** runtime for improved performance and security
- **Docker containerization** for consistent deployments
- **Cloud Run compatibility** for scalable serverless execution
- **PubSub event processing** for reliable message handling
- **Database connectivity** with Cloud SQL
- **Email sending** via SendGrid
- **Batch processing** with concurrent execution
- **Comprehensive logging** and error handling

## 📋 Prerequisites

- Python 3.13.5 or later
- Docker
- Google Cloud SDK (`gcloud`)
- Google Cloud Project with billing enabled

## 🏗️ Project Structure

```
├── main.py                 # Cloud Function entry point
├── email_helper.py         # Email processing module
├── storage_manager.py      # GCS operations
├── db_manager.py          # Database operations
├── utils.py               # Utility functions
├── requirements.txt       # Python dependencies
├── Dockerfile             # Docker configuration
├── .dockerignore          # Docker ignore rules
├── .gitignore             # Git ignore rules
├── cloudbuild.yaml        # Cloud Build configuration
├── deploy.sh              # Deployment script
├── test_compatibility.py  # Compatibility test script
└── README.md              # This file
```

## 🔧 Setup and Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd bfsi-cf-email-notice
```

### 2. Set Up Python Environment

```bash
# Create virtual environment
python3.13 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Test Compatibility

```bash
python test_compatibility.py
```

### 4. Configure Environment Variables

Create a `.env` file with your configuration:

```env
# Google Cloud Configuration
PROJECT_ID=your-project-id
REGION=us-central1

# Database Configuration
INSTANCE_CONNECTION_NAME=your-instance-connection-name
DB_NAME=your-database-name
DB_USER=your-database-user
DB_PASSWORD=your-database-password

# Email Configuration
SENDGRID_API_KEY=your-sendgrid-api-key
MAIL_ID=<EMAIL>

# Storage Configuration
GCS_BUCKET_NAME=your-bucket-name
GOOGLE_APPLICATION_CREDENTIALS_JSON={"type":"service_account",...}

# Processing Configuration
EMAIL_SEND_DELAY=0.05
MAX_CONCURRENT_EMAILS=5
MAX_RETRIES=3
SEND_WITHOUT_ATTACHMENT_ON_MISSING=true
```

## 🚀 Deployment

### Option 1: Automated Deployment

```bash
# Make the deployment script executable
chmod +x deploy.sh

# Set your project ID
export PROJECT_ID=your-project-id

# Run deployment
./deploy.sh
```

### Option 2: Manual Deployment

```bash
# Build Docker image
docker build -t gcr.io/your-project-id/bfsi-cf-email-notice .

# Push to Container Registry
docker push gcr.io/your-project-id/bfsi-cf-email-notice

# Deploy to Cloud Run
gcloud run deploy bfsi-cf-email-notice \
    --image gcr.io/your-project-id/bfsi-cf-email-notice \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --port 8080 \
    --memory 2Gi \
    --cpu 2 \
    --timeout 900
```

### Option 3: Cloud Build

```bash
# Submit build to Cloud Build
gcloud builds submit --config cloudbuild.yaml
```

## 🧪 Testing

### Local Testing

```bash
# Run compatibility tests
python test_compatibility.py

# Test individual modules
python -c "import main; print('Main module imported successfully')"
```

### Docker Testing

```bash
# Build and run locally
docker build -t bfsi-email-notice .
docker run -p 8080:8080 bfsi-email-notice
```

## 📊 Monitoring and Logging

The application includes comprehensive logging:

- **Cloud Logging** integration for centralized logs
- **Health checks** for service monitoring
- **Error tracking** with detailed stack traces
- **Performance metrics** for batch processing

## 🔒 Security Features

- **Non-root user** in Docker container
- **Minimal base image** (Python 3.13.5-slim)
- **Environment variable** configuration
- **Service account** authentication
- **Network security** with Cloud Run

## 🔄 Upgrade Notes

### From Python 3.9.0 to 3.13.5

This upgrade includes:

- ✅ **Performance improvements** with Python 3.13.5
- ✅ **Enhanced security** features
- ✅ **Better error handling** and debugging
- ✅ **Improved memory management**
- ✅ **Updated dependencies** for compatibility

### Breaking Changes

- None expected for this codebase
- All existing functionality preserved
- Environment variables remain the same

## 🛠️ Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Verify Python version
   python --version
   
   # Check installed packages
   pip list
   ```

2. **Docker Build Failures**
   ```bash
   # Check Docker daemon
   docker info
   
   # Clear Docker cache
   docker system prune
   ```

3. **Deployment Issues**
   ```bash
   # Check gcloud authentication
   gcloud auth list
   
   # Verify project settings
   gcloud config list
   ```

## 📚 Dependencies

### Core Dependencies

- `functions-framework==3.5.0` - Cloud Functions framework
- `google-cloud-storage==2.17.0` - Google Cloud Storage client
- `google-cloud-pubsub==2.28.0` - Google Cloud Pub/Sub client
- `cloud-sql-python-connector==1.12.0` - Cloud SQL connector
- `SQLAlchemy==2.0.36` - SQL toolkit
- `pandas==2.2.3` - Data manipulation
- `sendgrid==6.11.0` - Email service
- `requests==2.32.3` - HTTP library

### Runtime Dependencies

- `gunicorn==21.2.0` - WSGI HTTP Server
- `psycopg2-binary==2.9.9` - PostgreSQL adapter

## 📄 License

[Add your license information here]

## 🤝 Contributing

[Add contributing guidelines here]

## 📞 Support

[Add support contact information here]
