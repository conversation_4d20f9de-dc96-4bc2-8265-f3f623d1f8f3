
import json
import io
import pandas as pd
import logging
import base64
import time
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import functions_framework
from datetime import datetime, timezone
import pytz
import sqlalchemy
import os
from google.cloud.sql.connector import Connector
from sqlalchemy import text

from storage_manager import read_file_from_gcs, GCS_BUCKET_NAME
from email_helper import send_email, send_conciliation_email
from db_manager import update_campaign_status, get_dispute_ids_by_loan_ids, update_master_excel_status
from utils import prepare_conciliation_email_parameters, utc_now, ist_now, to_utc, to_ist

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Batch processing configuration
EMAIL_SEND_DELAY = float(os.environ.get('EMAIL_SEND_DELAY', 0.05))  # seconds between emails
MAX_CONCURRENT_EMAILS = int(os.environ.get('MAX_CONCURRENT_EMAILS', 5))
MAX_RETRIES = int(os.environ.get('MAX_RETRIES', 3))

pool = None

def connect_to_instance() -> sqlalchemy.engine.base.Engine:    
    connector = Connector(refresh_strategy="lazy")
    instance_conn_name = os.environ['INSTANCE_CONNECTION_NAME']
    db_name = os.environ.get('DB_NAME')
    db_user = os.environ.get('DB_USER')
    db_password = os.environ.get('DB_PASSWORD')
    def getconn():
        return connector.connect(
            instance_conn_name,
            'pg8000',
            user=db_user,
            password=db_password,
            db=db_name
        )

    return sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator      = getconn,
        pool_size    = 100,
        max_overflow = 20,
        pool_timeout = 30,
        pool_recycle = 300
    )

@functions_framework.cloud_event
def process_email_campaign(cloud_event):
    """Process Email campaign messages from PubSub with batching and parallel processing."""
    start_time = utc_now()  # Changed to UTC
    batch_errors = []  # Track errors during processing

    try:
        # Extract data from the cloud event
        if cloud_event.data:
            event_data = cloud_event.data

            # If the data is base64 encoded (common with Pub/Sub), decode it
            if isinstance(event_data, dict) and 'message' in event_data and 'data' in event_data['message']:
                import base64
                encoded_data = event_data['message']['data']
                decoded_data = base64.b64decode(encoded_data).decode('utf-8')
                try:
                    request_data = json.loads(decoded_data)
                except json.JSONDecodeError:
                    logger.error("Failed to parse cloud event data as JSON")
                    return json.dumps({'error': 'Invalid JSON in cloud event data'})
            else:
                # Handle other cloud event data formats if needed
                request_data = event_data
        else:
            logger.error("No data in cloud event")
            return json.dumps({'error': 'No data in cloud event'})

        # Extract parameters
        campaign_id = request_data.get('campaign_id')
        root_template_id = request_data.get('root_template_id')
        template_name = request_data.get('template_name')
        template_id = request_data.get('template_id')
        email_template_id = request_data.get('email_template_id')
        attachment_required = request_data.get('attachment_required')
        excel_file_path = request_data.get('excel_file_path')
        pdf_file_path = request_data.get('pdf_file_path')
        user_id = request_data.get('user_id')
        batch_number = request_data.get('batch_number', 0)
        total_batches = request_data.get('total_batches', 1)
        is_s21_notice = request_data.get('is_s21_notice')
        has_co_borrowers = request_data.get('has_co_borrowers', False)
        file_upload = request_data.get('file_upload', False)
        folder_name = request_data.get('folder_name', False)
        df_json = request_data.get('df_json')
        client_name = request_data.get('client_name')
        flow_type = request_data.get('flow_type', 'arbitration')  # Default to arbitration
        file_selection = request_data.get('file_selection', True)  # FIXED: Add this missing parameter

        # Conciliation-specific parameters
        is_termination_notice = request_data.get('is_termination_notice', False)
        is_s138_notice = request_data.get('is_s138_notice', False)
        is_payment_request_notice = request_data.get('is_payment_request_notice', False)
        sarfaeri_act_notice = request_data.get('sarfaeri_act_notice', False)

        conciliation_notice_1 = request_data.get('conciliation_notice_1', False)
        conciliation_notice_4 = request_data.get('conciliation_notice_4', False)
        conciliation_email_reminder = request_data.get('conciliation_email_reminder', False)
        template_parameter_mapping = request_data.get('template_parameter_mapping', {})

        # NEW: Extract Sheet 2 data for conciliation flow
        sheet2_data = request_data.get('sheet2_data', {})
        logger.info(f"Sheet2 data received: {sheet2_data}")

        # Define sub-batch size - each main batch will be broken into smaller chunks
        SUB_BATCH_SIZE = 20  # Adjust based on your rate limits and performance needs

        bucket_name = GCS_BUCKET_NAME

        # Connect to database
        global pool
        if not pool:
            pool = connect_to_instance()

        # Get the current state (emails sent count and processed_rows) if this is not the first batch
        current_emails_sent = 0
        current_processed_rows = 0
        try:
            # Query to get the current state
            query = text("""
                SELECT email_messages_sent, email_processed_rows 
                FROM notice_emailtemplate 
                WHERE id = :template_id
            """)
            
            with pool.connect() as connection:
                result = connection.execute(query, {"template_id": template_id})
                row = result.fetchone()
                if row:
                    current_emails_sent = row[0] if row[0] is not None else 0
                    current_processed_rows = row[1] if row[1] is not None else 0
        except Exception as e:
            logger.error(f"Error getting current state: {str(e)}")
            # Continue with defaults

        # Ensure processed_rows only increases sequentially
        batch_start_processed = current_processed_rows

        # Update campaign status to 'sending' if this is the first batch
        if batch_number == 0:
            update_success = update_campaign_status(pool, campaign_id, template_id, 'processing', 0, 0)

        # Parse batch data
        if df_json:
            df = pd.read_json(df_json, orient='records')
        else:
            error_msg = "No batch data provided"
            logger.error(error_msg)
            batch_errors.append(error_msg)
            update_campaign_status(pool, campaign_id, template_id, 'failed', current_processed_rows, current_emails_sent)
            return json.dumps({'error': error_msg})

        # Divide the batch into sub-batches
        total_records = len(df)
        num_sub_batches = (total_records + SUB_BATCH_SIZE - 1) // SUB_BATCH_SIZE  # Ceiling division

        # Track overall results
        results = []
        successful_count = 0
        failed_count = 0
        email_entries = []
        loan_ids = df['Loan ID'].tolist()
        loan_to_dispute_map = get_dispute_ids_by_loan_ids(pool, loan_ids, campaign_id)

        # Check if we have co-borrower columns in the dataframe
        co_borrower_columns = []
        if has_co_borrowers:
            # Check which co-borrower email columns actually exist in the dataframe
            for i in range(1, 5):  # Up to 4 co-borrowers
                email_col = f"Co-borrower's Email{i}"
                if email_col in df.columns:
                    co_borrower_columns.append(email_col)

        # Process each sub-batch separately
        for sub_batch_idx in range(num_sub_batches):
            sub_batch_start = sub_batch_idx * SUB_BATCH_SIZE
            sub_batch_end = min(sub_batch_start + SUB_BATCH_SIZE, total_records)
            sub_df = df.iloc[sub_batch_start:sub_batch_end]

            sub_batch_start_time = time.time()

            # Calculate the sequential processed rows for this sub-batch
            processed_rows_for_update = batch_start_processed + sub_batch_end

            file_data_map = {}
            missing_files_report = []
            valid_records_df = None

            # Add new parameter to control behavior when files are missing
            SEND_WITHOUT_ATTACHMENT_ON_MISSING = os.environ.get('SEND_WITHOUT_ATTACHMENT_ON_MISSING', 'true').lower() == 'true'

            # Load files ONLY if attachment_required is True (regardless of S21 status)
            if attachment_required:
                valid_records = []  # Store ONLY records that have valid files
                skipped_records = []  # Store records that don't have files
                
                # Pre-validate and load files for emails that have them
                for idx, row in sub_df.iterrows():
                    loan_id = str(row['Loan ID']).strip()
                    
                    # Conditional PDF path based on file_upload parameter
                    if file_upload and folder_name:
                        base_path = f"notices/{campaign_id}/files/{folder_name}/{loan_id}"
                    elif file_upload:
                        base_path = f"notices/{campaign_id}/files/{loan_id}"
                    else:
                        base_path = f"notices/{campaign_id}/{root_template_id}/{template_name}/{loan_id}"
                    pdf_data = None
                    successful_path = None
                    extensions = ['.pdf', '.PDF']
                    attempted_paths = []
                    
                    for ext in extensions:
                        try:
                            loan_pdf_path = f"{base_path}{ext}"
                            attempted_paths.append(loan_pdf_path)
                            pdf_data = read_file_from_gcs(GCS_BUCKET_NAME, loan_pdf_path)
                            successful_path = loan_pdf_path
                            break  # Success! Exit the loop
                        except Exception as e:
                            continue
                    
                    # ONLY include records that have attachments
                    if pdf_data is not None:
                        file_data_map[loan_id] = pdf_data
                        valid_records.append(row)
                    else:
                        skipped_records.append(row)
                
                # Only process records that have attachments
                if valid_records:
                    valid_records_df = pd.DataFrame(valid_records).reset_index(drop=True)
                else:
                    continue  # Skip this sub-batch, move to next one
            else:
                # attachment_required is False - send all emails without attachments
                valid_records_df = sub_df.copy()
                file_data_map = {}

            # IMPORTANT: Use valid_records_df instead of df for all further processing
            if valid_records_df is None or len(valid_records_df) == 0:
                continue  # FIXED: Continue to next sub-batch instead of returning

            # Replace sub_df with valid_records_df for all subsequent operations
            sub_df = valid_records_df

            # 2. EMAIL SENDING PHASE
            
            # Create a list to store all emails to be sent (primary borrowers + co-borrowers)
            all_email_tasks = []
            
            # Process each row in the sub-batch
            for _, row in sub_df.iterrows():
                loan_id = str(row['Loan ID']).strip()
                
                # Add primary borrower email
                borrower_email_col = 'Borrower\'s Email'
                primary_email = str(row[borrower_email_col]).strip() if borrower_email_col in row and pd.notna(row[borrower_email_col]) else ''
                if primary_email and '@' in primary_email:
                    borrower_name_col = 'Name of the Borrower'
                    borrower_name = row.get(borrower_name_col, '').strip() if borrower_name_col in row and pd.notna(row[borrower_name_col]) else ''
                    all_email_tasks.append({
                        'email': primary_email,
                        'loan_id': loan_id,
                        'is_co_borrower': False,
                        'borrower_type': 'primary',
                        'borrower_name': borrower_name,
                        'row_data': row
                    })
                
                # Add co-borrower emails if available
                if has_co_borrowers:
                    for i in range(1, 5):  # Up to 4 co-borrowers
                        email_col = f"Co-borrower's Email{i}"
                        name_col = f"Name of the Co-borrower{i}"
                        
                        if email_col in row and pd.notna(row[email_col]):
                            co_email = str(row[email_col]).strip()
                            co_name = str(row[name_col]).strip() if name_col in row and pd.notna(row[name_col]) else ''
                            
                            if co_email and '@' in co_email:
                                all_email_tasks.append({
                                    'email': co_email,
                                    'loan_id': loan_id,
                                    'is_co_borrower': True,
                                    'borrower_type': f'co-borrower-{i}',
                                    'borrower_name': co_name,
                                    'row_data': row
                                })
            
            # Function to send a single email
            def send_single_email(task):
                """Helper function to send a single email"""
                try:
                    # Get email and loan ID
                    email = task['email']
                    loan_id = task['loan_id']
                    is_co_borrower = task['is_co_borrower']
                    borrower_type = task['borrower_type']
                    borrower_name = task.get('borrower_name', '')
                    row_data = task['row_data']

                    # Skip if email is invalid
                    if not email or '@' not in email:
                        return {
                            'email': email,
                            'loan_id': loan_id,
                            'status': 'failed',
                            'reason': 'Invalid email address',
                            'borrower_type': borrower_type
                        }

                    # Get the email attachments if available AND attachment_required is True
                    email_files = []
                    if attachment_required and loan_id in file_data_map:
                        # Format the file data correctly for SendGrid
                        file_data = file_data_map[loan_id]
                        email_files = [{
                            'file': file_data,
                            'name': f'{loan_id}.pdf'
                        }]
                    elif attachment_required and loan_id not in file_data_map:
                        # This should not happen if we filtered correctly above
                        logger.warning(f"Attachment required but not found for loan {loan_id}")
                    # If attachment_required is False, email_files stays empty (no attachments)

                    dispute_id = loan_to_dispute_map.get(loan_id)
                    if not dispute_id:
                        return {
                            'email': email,
                            'loan_id': loan_id,
                            'status': 'failed',
                            'reason': 'No dispute ID found for loan',
                            'borrower_type': borrower_type
                        }
                    
                    # Send the email based on flow type
                    if flow_type == 'conciliation':
                        # Prepare parameters based on template type
                        param_result = prepare_conciliation_email_parameters(
                            row_data,
                            client_name,
                            is_termination_notice,
                            is_s138_notice,
                            is_payment_request_notice,
                            sarfaeri_act_notice,
                            conciliation_notice_1,
                            conciliation_notice_4,
                            conciliation_email_reminder,
                            template_parameter_mapping,
                            sheet2_data
                        )

                        # Extract parameters and template info
                        email_parameters = param_result.get('parameters', [])
                        template_type = param_result.get('template_type', 'generic')
                        custom_mapping = param_result.get('custom_parameter_mapping', None)

                        # Send conciliation email with proper template type and mapping
                        response = send_conciliation_email(
                            pool=pool,
                            email=email,
                            template_id=email_template_id,
                            dispute_id=dispute_id,
                            root_template_id=root_template_id,
                            is_s21_notice=is_s21_notice,
                            files=email_files,
                            is_co_borrower=is_co_borrower,
                            borrower_name=borrower_name,
                            email_parameters=email_parameters,
                            template_type=template_type,
                            custom_parameter_mapping=custom_mapping
                        )
                    else:
                        # Original arbitration flow
                        response = send_email(
                            pool=pool,
                            email=email,
                            template_id=email_template_id,
                            dispute_id=dispute_id,
                            root_template_id=root_template_id,
                            is_s21_notice=is_s21_notice,
                            client_name=client_name if client_name else None,
                            files=email_files,
                            is_co_borrower=is_co_borrower,
                            borrower_name=borrower_name
                        )

                    # Check if the email was sent successfully
                    if response and isinstance(response, str) and ('202' in response or 'success' in response):
                        # Extract message ID from response if available
                        msg_id = None
                        if 'x_message_id' in response:
                            msg_id = response.split('x_message_id')[1].split('\n')[0].strip()

                        return {
                            'email': email,
                            'loan_id': loan_id,
                            'status': 'success',
                            'msg_id': msg_id,
                            'borrower_type': borrower_type
                        }
                    else:
                        return {
                            'email': email,
                            'loan_id': loan_id,
                            'status': 'failed',
                            'reason': f'Email sending failed: {response}',
                            'borrower_type': borrower_type
                        }

                except Exception as e:
                    return {
                        'email': task.get('email', 'unknown'),
                        'loan_id': task.get('loan_id', 'unknown'),
                        'status': 'failed',
                        'reason': str(e),
                        'borrower_type': task.get('borrower_type', 'unknown')
                    }

            # Use ThreadPoolExecutor for parallel email sending
            sub_batch_results = []
            
            if not all_email_tasks:  # FIXED: Add check for empty task list
                continue
            
            with ThreadPoolExecutor(max_workers=min(MAX_CONCURRENT_EMAILS, len(all_email_tasks))) as executor:
                future_to_task = {executor.submit(send_single_email, task): task for task in all_email_tasks}

                for future in as_completed(future_to_task):
                    try:
                        result = future.result()
                        sub_batch_results.append(result)
                        
                        # Add successful emails to database entries batch
                        if result['status'] == 'success':
                            successful_count += 1
                            email_entries.append({
                                'loan_id': result['loan_id'],
                                'msg_id': result.get('msg_id', 'unknown'),
                                'email': result['email'],
                                'status': 'sent',
                                'timestamp': utc_now(),  # Changed to UTC
                                'user_id': user_id,
                                'is_co_borrower': result['borrower_type'] != 'primary',
                                'borrower_type': result['borrower_type']
                            })
                        else:
                            failed_count += 1

                    except Exception as e:
                        task = future_to_task[future]
                        logger.error(f"Unhandled exception in email send: {str(e)}")
                        sub_batch_results.append({
                            'email': task.get('email', 'unknown'),
                            'loan_id': task.get('loan_id', 'unknown'),
                            'status': 'failed',
                            'reason': f'Exception: {str(e)}',
                            'borrower_type': task.get('borrower_type', 'unknown')
                        })
                        failed_count += 1

            # Add sub-batch results to overall results
            results.extend(sub_batch_results)

            # Calculate and log metrics for this sub-batch
            sub_batch_time = time.time() - sub_batch_start_time
            sub_batch_success = sum(1 for r in sub_batch_results if r.get('status') == 'success')
            primary_count = sum(1 for r in sub_batch_results if r.get('borrower_type') == 'primary')
            co_borrower_count = sub_batch_success - primary_count

            # Small delay between sub-batches
            if sub_batch_idx < num_sub_batches - 1:
                time.sleep(0.5)  # Half second delay between sub-batches

            # Calculate total emails sent (existing + current batch)
            total_emails_sent = current_emails_sent + successful_count

            # Update campaign progress frequently
            update_success = update_campaign_status(
                pool,
                campaign_id,
                template_id, 
                'processing', 
                processed_rows=processed_rows_for_update,
                emails_sent=total_emails_sent
            )

        # # Batch insert all email entries into database
        # if email_entries:
        #     create_email_entries_batch(pool, email_entries)

        # Calculate overall batch processing time
        processing_time = (utc_now() - start_time).total_seconds()  # Changed to UTC

        # Calculate total emails sent (existing + current batch)
        total_emails_sent = current_emails_sent + successful_count

        # Final status update - determine if this was the last batch
        try:
            is_last_batch = (batch_number + 1) >= total_batches
            email_status = 'completed' if is_last_batch else 'processing'
            
            # Calculate final processed rows
            final_processed_rows = batch_start_processed + total_records
                
            # Track processing success rate in errors if not perfect
            if failed_count > 0:
                summary_msg = f"Completed with {failed_count} failures out of {len(results)} emails ({(failed_count/len(results))*100:.1f}% failure rate)"
                batch_errors.append(summary_msg)
            
            # Make final update and verify it worked
            final_update_success = update_campaign_status(
                pool=pool,
                campaign_id=campaign_id,
                template_id=template_id,
                email_status=email_status,
                processed_rows=final_processed_rows,
                emails_sent=total_emails_sent
            )
            
            # If the update failed, try one more time
            if not final_update_success and is_last_batch:
                time.sleep(1)  # Brief delay
                update_campaign_status(
                    pool=pool,
                    campaign_id=campaign_id,
                    template_id=template_id,
                    email_status='completed',
                    processed_rows=final_processed_rows,
                    emails_sent=total_emails_sent
                )
                
        except Exception as e:
            error_msg = f"Error in final status update: {str(e)}"
            logger.error(error_msg)
            batch_errors.append(error_msg)
            
            # Try a last-ditch effort to update status if this was the last batch
            if 'is_last_batch' in locals() and is_last_batch:
                try:
                    update_campaign_status(
                        pool=pool, 
                        campaign_id=campaign_id,
                        template_id=template_id,
                        email_status='completed',
                        processed_rows=batch_start_processed + total_records,
                        emails_sent=total_emails_sent
                    )
                except Exception as final_e:
                    logger.error(f"Failed emergency final status update: {str(final_e)}")

        # Log the completion with co-borrower breakdown
        primary_sent = sum(1 for r in results if r.get('status') == 'success' and r.get('borrower_type') == 'primary')
        co_borrower_sent = successful_count - primary_sent

        # Update master_excel_generated status when batch completes
        if campaign_id and pool:
            logger.info(f"Updating master_excel_generated status for campaign {campaign_id}")
            try:
                update_result = update_master_excel_status(pool, campaign_id, status=False)
                if update_result:
                    logger.info(f"Successfully updated master_excel_generated to False for campaign {campaign_id}")
                else:
                    logger.error(f"Failed to update master_excel_generated for campaign {campaign_id}")
            except Exception as e:
                logger.error(f"Exception updating master_excel_generated for campaign {campaign_id}: {str(e)}")

        # Return processing data
        return {
            'success': True,
            'campaign_id': campaign_id,
            'batch_number': batch_number,
            'total_processed': final_processed_rows if 'final_processed_rows' in locals() else batch_start_processed + total_records,
            'successful': successful_count,
            'primary_sent': primary_sent,
            'co_borrower_sent': co_borrower_sent,
            'total_emails_sent': total_emails_sent,
            'failed': failed_count,
            'processing_time': processing_time,
            'errors': len(batch_errors),
            'flow_type': flow_type,
            'has_co_borrowers': has_co_borrowers
        }

    except Exception as e:
        error_msg = f"Error in process_email_campaign: {str(e)}"
        logger.exception(error_msg)
        batch_errors.append(error_msg)
        
        # Try to update status to failed if we have the necessary info
        if 'pool' in locals() and 'campaign_id' in locals() and 'template_id' in locals():
            try:
                # Determine current emails sent count if possible
                total_emails = current_emails_sent if 'current_emails_sent' in locals() else 0
                if 'successful_count' in locals():
                    total_emails += successful_count
                    
                # Determine processed rows
                processed = current_processed_rows if 'current_processed_rows' in locals() else 0
                
                update_campaign_status(
                    pool, 
                    campaign_id, 
                    template_id, 
                    'failed',
                    processed_rows=processed,
                    emails_sent=total_emails
                )
            except Exception as update_e:
                logger.error(f"Failed to update status to failed after exception: {str(update_e)}")
        
        # Return error information
        return {
            'success': False,
            'error': str(e),
            'errors': batch_errors,
            'flow_type': flow_type if 'flow_type' in locals() else 'unknown'
        }

    finally:
        if campaign_id and 'pool' in locals() and pool:
            logger.info(f"FINALLY block: Ensuring master_excel_generated is updated for campaign {campaign_id}")
            try:
                update_result = update_master_excel_status(pool, campaign_id, status=False)
                if update_result:
                    logger.info(f"FINALLY: Successfully updated master_excel_generated to False for campaign {campaign_id}")
                else:
                    logger.error(f"FINALLY: Failed to update master_excel_generated for campaign {campaign_id}")
            except Exception as e:
                logger.error(f"FINALLY: Exception updating master_excel_generated for campaign {campaign_id}: {str(e)}")
