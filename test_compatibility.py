#!/usr/bin/env python3
"""
Test script to validate Python 3.13.5 compatibility with all dependencies
and Cloud Function components.
"""

import sys
import importlib
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_python_version():
    """Test that we're running Python 3.13.5"""
    version = sys.version_info
    logger.info(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor != 13:
        logger.warning(f"Expected Python 3.13.x, got {version.major}.{version.minor}.{version.micro}")
        return False
    
    logger.info("✅ Python version check passed")
    return True

def test_imports():
    """Test that all required modules can be imported"""
    required_modules = [
        'functions_framework',
        'google.cloud.storage',
        'google.cloud.pubsub',
        'google.cloud.sql.connector',
        'sqlalchemy',
        'pandas',
        'openpyxl',
        'requests',
        'sendgrid',
        'psycopg2',
        'json',
        'io',
        'logging',
        'base64',
        'time',
        'concurrent.futures',
        'datetime',
        'pytz',
        'os'
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            importlib.import_module(module)
            logger.info(f"✅ Successfully imported {module}")
        except ImportError as e:
            logger.error(f"❌ Failed to import {module}: {e}")
            failed_imports.append(module)
    
    if failed_imports:
        logger.error(f"Failed to import: {failed_imports}")
        return False
    
    logger.info("✅ All module imports successful")
    return True

def test_cloud_function_decorator():
    """Test that the functions_framework decorator works"""
    try:
        from functions_framework import cloud_event
        
        @cloud_event
        def test_function(cloud_event):
            return {"status": "success"}
        
        logger.info("✅ Cloud function decorator test passed")
        return True
    except Exception as e:
        logger.error(f"❌ Cloud function decorator test failed: {e}")
        return False

def test_database_connection_setup():
    """Test that database connection components work"""
    try:
        from google.cloud.sql.connector import Connector
        import sqlalchemy
        
        # Test that we can create a connector instance
        connector = Connector(refresh_strategy="lazy")
        logger.info("✅ Database connector initialization successful")
        
        # Test SQLAlchemy engine creation (without actual connection)
        engine_url = "postgresql+pg8000://"
        logger.info("✅ Database connection setup test passed")
        return True
    except Exception as e:
        logger.error(f"❌ Database connection setup test failed: {e}")
        return False

def test_email_components():
    """Test that email sending components work"""
    try:
        import sendgrid
        from sendgrid.helpers.mail import Mail, Personalization, To
        
        # Test SendGrid client creation (without API key)
        logger.info("✅ Email components test passed")
        return True
    except Exception as e:
        logger.error(f"❌ Email components test failed: {e}")
        return False

def test_data_processing():
    """Test that data processing components work"""
    try:
        import pandas as pd
        import openpyxl
        import json
        
        # Test basic pandas operations
        test_data = {"test": [1, 2, 3]}
        df = pd.DataFrame(test_data)
        json_str = df.to_json(orient='records')
        df_restored = pd.read_json(json_str, orient='records')
        
        logger.info("✅ Data processing components test passed")
        return True
    except Exception as e:
        logger.error(f"❌ Data processing components test failed: {e}")
        return False

def main():
    """Run all compatibility tests"""
    logger.info("Starting Python 3.13.5 compatibility tests...")
    
    tests = [
        ("Python Version", test_python_version),
        ("Module Imports", test_imports),
        ("Cloud Function Decorator", test_cloud_function_decorator),
        ("Database Connection Setup", test_database_connection_setup),
        ("Email Components", test_email_components),
        ("Data Processing", test_data_processing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        if test_func():
            passed += 1
        else:
            logger.error(f"Test {test_name} failed!")
    
    logger.info(f"\n=== Test Results ===")
    logger.info(f"Passed: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 All compatibility tests passed!")
        return True
    else:
        logger.error(f"❌ {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
