
import logging
import pandas as pd
from datetime import datetime, time, timezone 
import pytz

# Configure logging
logger = logging.getLogger(__name__)

# Timezone definitions
UTC = timezone.utc
IST = pytz.timezone('Asia/Kolkata')

def utc_now():
    """Get current UTC datetime"""
    return datetime.now(UTC)

def ist_now():
    """Get current IST datetime"""
    return datetime.now(IST)

def to_utc(dt):
    """Convert datetime to UTC"""
    if dt is None:
        return None
    
    if isinstance(dt, str):
        try:
            dt = pd.to_datetime(dt)
        except Exception as e:
            logger.error(f"to_utc: Failed to parse string datetime '{dt}': {str(e)}")
            return None
    
    if dt.tzinfo is None:
        logger.warning(f"to_utc: Naive datetime {dt} assumed to be in IST")
        dt = IST.localize(dt)
    else:
        logger.debug(f"to_utc: Converting timezone-aware datetime {dt} to UTC")
    
    return dt.astimezone(UTC)

def to_ist(dt):
    """Convert datetime to IST for display purposes"""
    if dt is None:
        return None
        
    if isinstance(dt, str):
        # Try to parse string datetime
        try:
            dt = pd.to_datetime(dt)
        except:
            return None
    
    if dt.tzinfo is None:
        # Assume UTC if no timezone info when converting to IST
        dt = UTC.replace(dt)
    
    return dt.astimezone(IST)

def determine_template_type(is_termination_notice, is_demand_notice, is_payment_request_notice, sarfaeri_act_notice, conciliation_notice_1, 
                          conciliation_notice_4, conciliation_email_reminder):
    """
    Determine the template type based on the boolean flags.
    
    Returns:
        str: The template type identifier
    """
    if is_termination_notice:
        return 'termination_notice'
    elif is_demand_notice:
        return 'demand_notice'
    elif is_payment_request_notice:
        return 'payment_request_notice'
    elif sarfaeri_act_notice:
        return 'sarfaeri_act_notice'
    elif conciliation_email_reminder:
        return 'conciliation_email_reminder'
    elif conciliation_notice_1:
        return 'conciliation_notice_1'
    elif conciliation_notice_4:
        return 'conciliation_notice_4'
    else:
        return 'generic'

def prepare_conciliation_email_parameters(row_data, client_name, is_termination_notice, is_demand_notice, is_payment_request_notice, sarfaeri_act_notice,
                                         conciliation_notice_1, conciliation_notice_4, conciliation_email_reminder,
                                         template_parameter_mapping, sheet2_data):
    """
    Prepare parameters for conciliation email templates based on template type and row data.
    Returns both parameters and template type for better handling.
    """
    parameters = []
    template_type = determine_template_type(
        is_termination_notice, is_demand_notice, is_payment_request_notice, sarfaeri_act_notice, conciliation_notice_1, 
        conciliation_notice_4, conciliation_email_reminder
    )

    try:
        if is_termination_notice:
            # PFL Termination Notice parameters
            parameters = [
                str(client_name),
                str(row_data.get('Name of the Borrower', '')),
                str(row_data.get('Loan ID', '')),
                parse_excel_date(row_data.get('Notice Date', '')),
                format_currency(row_data.get('Notice Amount', 0)),
                str(row_data.get('Payment Link', ''))
            ]

        elif is_demand_notice:
            # Demand Notice parameters
            parameters = [
                str(row_data.get('Name of the Borrower', '')),
                str(client_name),
                str(row_data.get('Loan ID', '')),
                parse_excel_date(row_data.get('Dishonour Date', '')),
                parse_excel_date(row_data.get('Notice Date', '')),
                format_currency(row_data.get('Notice Amount', 0)),
                str(row_data.get('Payment Link', '')),
            ]
        
        elif is_payment_request_notice:
            parameters = [
                str(row_data.get('Name of the Borrower', '')),
                str(client_name),
                str(row_data.get('Loan ID', '')),
                parse_excel_date(row_data.get('Notice Date', '')),
                format_currency(row_data.get('Notice Amount', 0)),
                # format_currency(row_data.get('Notice Amount', 0)),
                str(row_data.get('Payment Link', '')),
            ]
        elif sarfaeri_act_notice:
            parameters = [
                str(row_data.get('Name of the Borrower', '')),
                str(client_name),
                str(row_data.get('Payment Link', '')),
            ]

        elif conciliation_email_reminder:
            # Payment Request Notice parameters
            
            if sheet2_data:
                parameters = [
                parse_excel_date(sheet2_data.get('date', '')),
                str(client_name),
                parse_excel_date(sheet2_data.get('date', '')),
                format_time(sheet2_data.get('start_time', '')),
                format_time(sheet2_data.get('end_time', '')),
                str(sheet2_data.get('link', ''))
                ]

            else:
                parameters = [
                parse_excel_date(sheet2_data.get('date', '')),
                str(client_name),
                parse_excel_date(sheet2_data.get('date', '')),
                format_time(sheet2_data.get('start_time', '')),
                format_time(sheet2_data.get('end_time', '')),
                str(sheet2_data.get('link', ''))
                ]

        elif conciliation_notice_1:
            # Conciliation Notice 1 parameter
            if sheet2_data:
                parameters = [
                    str(client_name),
                    parse_excel_date(sheet2_data.get('date', '')),
                    format_time(sheet2_data.get('start_time', '')),
                    format_time(sheet2_data.get('end_time', '')),
                    str(sheet2_data.get('case_manager_name', '')),
                    str(sheet2_data.get('case_manager_email', '')),
                    str(sheet2_data.get('link', '')),
                ]
            else:
                parameters = [
                    str(client_name),
                    parse_excel_date(row_data.get('date', '')),
                    format_time(row_data.get('start_time', '')),
                    format_time(row_data.get('end_time', '')),
                    str(row_data.get('case_manager_name', '')),
                    str(row_data.get('case_manager_email', '')),
                    str(row_data.get('link', '')),
                ]

        elif conciliation_notice_4:
            # Conciliation Notice 4 parameters
            if sheet2_data:
                parameters = [
                    parse_excel_date(sheet2_data.get('date', '')),
                    str(sheet2_data.get('link', '')),
                ]
            else:
                parameters = [
                    parse_excel_date(row_data.get('date', '')),
                    str(row_data.get('link', '')),
                ]

        elif template_parameter_mapping:
            # Generic template with custom parameter mapping
            # Create parameters based on the custom mapping order
            sorted_mapping = sorted(template_parameter_mapping.items())
            custom_param_keys = []

            for position, column_name in sorted_mapping:
                custom_param_keys.append(column_name)
                value = ''
                if sheet2_data and column_name in sheet2_data:
                    value = sheet2_data.get(column_name, '')
                elif column_name in row_data:
                    value = row_data.get(column_name, '')

                if pd.isna(value):
                    value = ''
                elif isinstance(value, (int, float)):
                    if column_name.lower() in ['amount', 'notice_amount', 'payment', 'outstanding_amount',
                                               'final_amount', 'settlement_amount', 'reminder_amount']:
                        value = format_currency(value)
                    else:
                        value = str(int(value)) if value == int(value) else str(value)
                else:
                    value = str(value)

                parameters.append(value)
            
            # Return both parameters and the custom mapping for this template
            return {
                'parameters': parameters,
                'template_type': 'custom',
                'custom_parameter_mapping': custom_param_keys
            }

        else:
            parameters = [str(row_data.get('Loan ID', ''))]

        parameters = [str(param) if param is not None else '' for param in parameters]
        
        return {
            'parameters': parameters,
            'template_type': template_type
        }

    except Exception as e:
        logger.error(f"Error preparing conciliation email parameters: {str(e)}")
        return [str(row_data.get('Loan ID', ''))]

def parse_excel_date(value):
    """Parse Excel date and return in DD-MM-YYYY format (display format)"""
    try:
        if isinstance(value, (int, float)) and value > 1e12:
            return pd.to_datetime(value, unit='ms').strftime('%d-%m-%Y')
        elif isinstance(value, (int, float)):
            return (pd.to_datetime('1899-12-30') + pd.to_timedelta(value, unit='D')).strftime('%d-%m-%Y')
        elif isinstance(value, str):
            return pd.to_datetime(value).strftime('%d-%m-%Y')
        elif isinstance(value, pd.Timestamp):
            return value.strftime('%d-%m-%Y')
    except Exception:
        pass
    return str(value)

def format_time(time_value):
    """Format time for display purposes"""
    try:
        if pd.isna(time_value) or time_value == '':
            return ''
        time_str = str(time_value)
        time_formats = ['%H:%M:%S', '%H:%M', '%I:%M %p', '%I:%M:%S %p']
        for fmt in time_formats:
            try:
                if fmt in ['%H:%M:%S', '%H:%M']:
                    parsed_time = datetime.strptime(time_str, fmt).time()
                    dt = datetime.combine(datetime.today(), parsed_time)
                    return dt.strftime('%I:%M %p')
                else:
                    parsed_time = datetime.strptime(time_str, fmt)
                    return parsed_time.strftime('%I:%M %p')
            except ValueError:
                continue
        try:
            hour = int(float(time_str))
            if 0 <= hour <= 23:
                dt = datetime.strptime(f"{hour:02d}:00", "%H:%M")
                return dt.strftime('%I:%M %p')
        except:
            pass
        return str(time_value)
    except Exception as e:
        return str(time_value)

def format_currency(amount):
    """Format currency for display"""
    try:
        if pd.isna(amount) or amount == '':
            return '₹0.00'
        amount = float(amount)
        return f"₹{amount:,.2f}"
    except (ValueError, TypeError):
        return '₹0.00'  